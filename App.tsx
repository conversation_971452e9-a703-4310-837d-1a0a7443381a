import React, {useEffect} from 'react';
import {SafeAreaView, StatusBar, View} from 'react-native';

import {Provider} from 'react-redux';
import store from './src/redux/store/store';
import {NavigationContainer} from '@react-navigation/native';
import {navigationRef} from './src/router/router';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {LanguageProvider} from './src/locales/languageContext';
import notifee from '@notifee/react-native';
import {
  initNotificationPermission,
  registerListenerWithFCM,
} from './src/features/notifications/fcm/fcm_helper';
import {PaperProvider} from 'react-native-paper';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {TableController} from './src/base/baseController';
import {ColorThemes} from './src/assets/skin/colors';
import {FSnackbar} from 'wini-mobile-components';
import {DrawerMain} from './src/Screen/Layout/navigation/drawerNavigation';
import {initBiometrics} from './src/features/local-authen/local-authen';

function App(): React.JSX.Element {
  useEffect(() => {
    notifee.setBadgeCount(0).then(() => console.log('Badge count removed'));
  }, []);

  /** setup firebase cloud message */
  useEffect(() => {
    initNotificationPermission();
    initBiometrics();
  }, []);

  useEffect(() => {
    const unsubscribe = registerListenerWithFCM();
    return unsubscribe;
  }, []);

  const [isLoading, setLoading] = React.useState(true);

  useEffect(() => {
    // Luôn cho phép ứng dụng khởi động, chỉ kiểm tra internet để load design tokens
    const initializeApp = async () => {
      try {
        // Kiểm tra kết nối internet với timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 giây timeout

        const response = await fetch('https://www.google.com', {
          method: 'HEAD',
          mode: 'no-cors',
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          // Có internet - load design tokens
          const colorData = new TableController('designtoken');
          colorData.getAll().then(res => {
            if (res.code == 200) {
              const designTokens = res.data.map((e: any) => {
                return {
                  ...e,
                  Value: typeof e.Value === 'string' ? JSON.parse(e.Value) : e.Value,
                };
              });
              const tokenValues = designTokens.filter(
                (e: any) => e.Type == 'group' || e.Type == 'color',
              );
              // check lọc tokenValues là type color có ParentId là type group sau đó mới thêm vào ColorThemes
              const colorTokens = tokenValues.filter((e: any) => e.Type == 'color');
              const groupTokens = tokenValues.filter((e: any) => e.Type == 'group');
              colorTokens.forEach((color: any) => {
                const group = groupTokens.find((e: any) => e.Id == color.ParentId);
                if (group) {
                  ColorThemes.light[
                    `${group.Name.replaceAll(' ', '_')}_${color.Name}`
                  ] = color.Value?.lightMode;
                  ColorThemes.dark[
                    `${group.Name.replaceAll(' ', '_')}_${color.Name}`
                  ] = color.Value?.darkMode;
                }
              });
            }
          }).catch(err => {
            console.log('Error loading design tokens:', err);
          });
        }
      } catch (error) {
        // Không có internet hoặc lỗi - vẫn cho phép ứng dụng chạy
        console.log('No internet connection or error:', error);
      } finally {
        // Luôn cho phép ứng dụng khởi động sau 2 giây
        setTimeout(() => {
          setLoading(false);
        }, 2000);
      }
    };

    initializeApp();
  }, []);

  return isLoading ? (
    <View style={{backgroundColor: 'white', flex: 1}} />
  ) : (
    // <WiniMobileProvider
    //   pid={ConfigAPI.pid}
    //   url={ConfigAPI.url}
    //   imgUrlId={ConfigAPI.urlImg}>
    <Provider store={store} stabilityCheck="always">
      <LanguageProvider>
        <PaperProvider>
          <GestureHandlerRootView>
            {/* <SafeAreaView style={{backgroundColor: 'white', flex: 1}}> */}
            <StatusBar barStyle={'dark-content'} backgroundColor={'white'} />
            <SafeAreaProvider>
              <NavigationContainer ref={navigationRef}>
                <DrawerMain />
                <FSnackbar />
              </NavigationContainer>
            </SafeAreaProvider>
            {/* </SafeAreaView> */}
          </GestureHandlerRootView>
        </PaperProvider>
      </LanguageProvider>
    </Provider>
    // </WiniMobileProvider>
  );
}
export default App;
